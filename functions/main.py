import threading
from firebase_admin import initialize_app, get_app, auth as admin_auth, firestore
from firebase_functions import https_fn, firestore_fn, scheduler_fn
from firebase_functions.https_fn import Request, Response
from openai import OpenAI
import os
import json
import requests
from urllib.parse import quote

# Initialize Firebase Admin SDK (for Firestore/Auth/etc. if needed)
_init_lock = threading.Lock()
_app = None
_db  = None
def ensure_app():
    global _app
    if _app is None:
        with _init_lock:
            if _app is None:
                # If another thread initialised while we waited, get_app() succeeds
                try:
                    _app = get_app()
                except ValueError:              # not initialised yet
                    _app = initialize_app()

def get_db():
    global _db
    ensure_app()
    if _db is None:
        with _init_lock:
            if _db is None:
                _db = firestore.client()
    return _db

@https_fn.on_request(secrets=["OPENAI_API_KEY"], timeout_sec=300)
def generate(req: Request) -> Response:
    ensure_app()
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    decoded_token = admin_auth.verify_id_token(id_token)
    uid = decoded_token["uid"]

    try:
        data = req.get_json(silent=True)

        if not data or "input" not in data:
            return Response(json.dumps({"error": "Missing 'input' in request body"}), status=400, headers={"Content-Type": "application/json"})

        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            return Response(json.dumps({"error": "OpenAI API key not found"}), status=500, headers={"Content-Type": "application/json"})

        client = OpenAI(api_key=api_key)

        # Create parameters dictionary with required fields
        params = {
            "model": data.get("model", "gpt-5-mini"),
            "input": data["input"],
            "instructions": data.get("instructions"),
            "text": data.get("text", {"format": {"type": "text"}}),
            "user": uid
        }

        # Add previous_response_id if it exists in the request
        if "previous_response_id" in data and data["previous_response_id"]:
            params["previous_response_id"] = data["previous_response_id"]

        response = client.responses.create(**params)

        return Response(json.dumps(response.model_dump()), status=200, headers={"Content-Type": "application/json"})

    except Exception as e:
        return Response(json.dumps({"error": str(e)}), status=500, headers={"Content-Type": "application/json"})


@https_fn.on_request(secrets=["UNSPLASH_ACCESS_KEY"], timeout_sec=10)
def get_unsplash_image(req: Request) -> Response:
    ensure_app()
    # Verify authentication
    id_token = req.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = admin_auth.verify_id_token(id_token)
        _ = decoded_token["uid"]
    except Exception as e:
        return Response(
            json.dumps({"error": "Unauthorized"}),
            status=401,
            headers={"Content-Type": "application/json"}
        )

    if req.method != "GET":
        return Response(
            json.dumps({"error": "Method not allowed. Only GET requests are supported."}),
            status=405,
            headers={"Content-Type": "application/json"}
        )

    try:
        query = req.args.get("query")

        if not query:
            return Response(
                json.dumps({"error": "Missing 'query' parameter"}),
                status=400,
                headers={"Content-Type": "application/json"}
            )

        # Get Unsplash API key from secrets
        unsplash_key = os.environ.get("UNSPLASH_ACCESS_KEY")
        if not unsplash_key:
            return Response(
                json.dumps({"error": "Unsplash API key not found"}),
                status=500,
                headers={"Content-Type": "application/json"}
            )

        # Call Unsplash API
        url = f"https://api.unsplash.com/search/photos?query={quote(query)}&per_page=1"
        headers = {
            "Authorization": f"Client-ID {unsplash_key}",
        }

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code != 200:
            return Response(
                json.dumps({
                    "error": f"Unsplash API returned status {response.status_code}",
                    "details": response.text
                }),
                status=response.status_code,
                headers={"Content-Type": "application/json"}
            )

        data = response.json()
        results = data.get("results", [])

        if not results:
            return Response(
                json.dumps({"error": "No images found for the given query"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        image_url = results[0].get("urls", {}).get("regular")

        if not image_url:
            return Response(
                json.dumps({"error": "Image URL not found in API response"}),
                status=404,
                headers={"Content-Type": "application/json"}
            )

        return Response(
            json.dumps({"imageUrl": image_url}),
            status=200,
            headers={"Content-Type": "application/json"}
        )

    except requests.exceptions.Timeout:
        return Response(
            json.dumps({"error": "Request to Unsplash API timed out"}),
            status=408,
            headers={"Content-Type": "application/json"}
        )
    except requests.exceptions.RequestException as e:
        return Response(
            json.dumps({"error": f"Network error: {str(e)}"}),
            status=503,
            headers={"Content-Type": "application/json"}
        )
    except Exception as e:
        return Response(
            json.dumps({"error": f"Internal server error: {str(e)}"}),
            status=500,
            headers={"Content-Type": "application/json"}
        )


def get_existing_recipe_ids(uid: str) -> list:
    """
    Retrieve existing recipe IDs for a user from Firestore subcollection.

    Args:
        uid: User's unique identifier

    Returns:
        list: List of existing recipe IDs, empty list if no recipes found
    """
    db = get_db()
    recipes_ref = db.collection('generatedRecipes').document(uid).collection('recipes')

    # Get only the document IDs (recipe IDs) without fetching full documents
    docs = recipes_ref.select([]).get()

    return [doc.id for doc in docs]


def get_user_diet_preferences(uid: str) -> dict:
    """
    Retrieve user's diet preferences from Firestore.

    Args:
        uid: User's unique identifier

    Returns:
        dict: User's diet preferences

    Raises:
        ValueError: If diet preferences not found
    """
    db = get_db()
    diet_prefs_ref = db.collection('dietPreferences').document(uid)
    diet_prefs_doc = diet_prefs_ref.get()

    if not diet_prefs_doc.exists:
        raise ValueError("Diet preferences not found. Please complete onboarding first.")

    return diet_prefs_doc.to_dict()


def generate_recipes_openai(diet_preferences: dict, uid: str, numRecipes: int = 3, existing_recipe_ids: list = None) -> list:
    """
    Generate recipes using OpenAI API based on user's diet preferences.

    Args:
        diet_preferences: User's dietary preferences and restrictions
        uid: User's unique identifier for API tracking
        numRecipes: Number of recipes to generate (default: 3)
        existing_recipe_ids: List of existing recipe IDs to avoid duplicating (optional)

    Returns:
        list: Generated recipes

    Raises:
        ValueError: If OpenAI API key not found or API call fails
        json.JSONDecodeError: If response parsing fails
    """
    # Get OpenAI API key
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("OpenAI API key not found")

    client = OpenAI(api_key=api_key)

    # Define the recipe generation schema
    # !!!! Keep this in sync with the TypeScript schema in app/schemas/recipeGeneration.ts !!!!
    recipe_generation_schema = {
        "type": "object",
        "properties": {
            "recipes": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "compatibleDiets": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "additionalProperties": False,
                            },
                        },
                        "title": {"type": "string"},
                        "timeInMinutes": {"type": "number"},
                        "calories": {"type": "number"},
                        "ingredients": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "amount": {"type": "string"},
                                    "unit": {"type": "string"},
                                    "available": {"type": "boolean"}
                                },
                                "required": ["name", "amount", "unit", "available"],
                                "additionalProperties": False
                            }
                        },
                        "instructions": {
                            "type": "object",
                            "properties": {
                                "High level": {"type": "string"},
                                "Detailed": {"type": "string"},
                                "Teach mode": {"type": "string"}
                            },
                            "required": ["High level", "Detailed", "Teach mode"],
                            "additionalProperties": False
                        },
                        "imageQuery": {"type": "string"},
                        "mealType": {
                            "type": "string",
                            "enum": ["Breakfast", "Lunch", "Dinner", "Dessert"]  
                        }
                    },
                    "required": ["id", "compatibleDiets", "title", "timeInMinutes", "calories", "ingredients", "instructions", "imageQuery", "mealType"],
                    "additionalProperties": False
                }
            }
        },
        "required": ["recipes"],
        "additionalProperties": False
    }

    # Create instructions for recipe generation
    existing_recipes_instruction = ""
    if existing_recipe_ids and len(existing_recipe_ids) > 0:
        existing_recipes_instruction = f"""
      IMPORTANT:
      - The user already has these recipe IDs in their collection: {existing_recipe_ids}.
      - DO NOT generate recipes that are duplicates or highly similar to these existing ones.
      - Ensure variety in cuisines and core ingredients across the generated recipes.
      """

    instructions = f"""
      You are a professional chef and nutritionist helping the user discover new, practical, and healthy recipes.

      User's dietary preferences:
      {json.dumps(diet_preferences)}

      Your goals:
      - Strictly follow the user's dietary preferences, restrictions, and lifestyle.
      - Generate diverse, nutritious, realistic recipes that fit the user's cooking experience.
      - Ensure accuracy in calorie estimates and ingredient measurements.

      {existing_recipes_instruction}

      TASK:
      Generate exactly {numRecipes} new recipes with the following distribution:
      - {numRecipes // 4} Breakfast
      - {numRecipes // 4} Lunch
      - {numRecipes // 4} Dinner
      - {numRecipes // 4} Dessert

      For each recipe, output ALL of the following fields:
      1. **dietHashSlugId**: <8-char SHA-256 hash>_<slugified-title>
        - Hash is computed over the canonical JSON of title + ingredients.
        - Example: `3fa9c2d7_chickpea-curry`
      2. **Title**: A descriptive, appetizing recipe title.
      3. **Preparation time**: Total minutes from start to finish (realistic).
      4. **Calories per serving**: Accurately based on specified ingredient amounts.
      5. **Ingredients**:
        - Precise serving size (amount + unit, e.g., "200 grams", "2 tablespoons").
        - Availability field (set all to `false` for now).
      6. **Instructions** (three versions):
        - **High Level**: Concise overview for experienced cooks.
        - **Detailed**: Step-by-step with clear explanations.
        - **Teach Mode**: Beginner-friendly with detailed cooking science, technique tips, and reasoning.
      7. **Image Search Query**: Exactly two words that clearly identify the dish as food (e.g., `mango curry`).
      8. **Meal Type**: Breakfast, Lunch, Dinner, or Dessert.

      RECIPE QUALITY RULES:
      - No duplicates or close variants of existing recipes.  
      - Use a wide range of cuisines, cooking methods, and key ingredients.  
      - Recipes must be realistic for a home cook, with accessible ingredients and steps.  
      - Balance health, flavor, and practicality.

      Final Output:  
      - Return results in valid JSON strictly following `recipe_generation_schema`. 
      """

    # Create the prompt
    prompt = f"""
      Generate {numRecipes} diverse, practical, and delicious recipes tailored to my dietary preferences.
      Include exactly {numRecipes // 4} recipes each for breakfast, lunch, dinner, and dessert.
      Every recipe must include precise ingredient amounts, realistic prep times, accurate calories, and all required fields.
      """

    # Prepare the API call parameters
    params = {
        "model": "gpt-5-mini",
        "input": [{"role": "developer", "content": prompt}],
        "instructions": instructions,
        "text": {
            "format": {
                "name": "recipes",
                "schema": recipe_generation_schema,
                "type": "json_schema"
            }
        },
        "user": uid
    }

    response = client.responses.create(**params)
    output_content = response.output_text

    if not output_content:
        raise ValueError("No response content from OpenAI API")

    # Parse the JSON response
    try:
        parsed_response = json.loads(output_content)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Failed to parse OpenAI response: {str(e)}", output_content, 0)

    if not parsed_response.get("recipes") or not isinstance(parsed_response["recipes"], list):
        raise ValueError("Invalid response format from OpenAI")

    recipes = parsed_response["recipes"]

    return recipes


def store_recipes(uid: str, recipes: list) -> None:
    """
    Store generated recipes in Firestore as individual documents in a subcollection.
    Each recipe is stored as a separate document in generatedRecipes/{uid}/recipes/{recipeId}

    Args:
        uid: User's unique identifier
        recipes: List of generated recipes

    Raises:
        Exception: If Firestore operation fails
    """
    db = get_db()

    # Get references to the user document and recipes subcollection
    user_doc_ref = db.collection('generatedRecipes').document(uid)
    recipes_collection_ref = user_doc_ref.collection('recipes')

    # Use a batch write for better performance and atomicity
    batch = db.batch()

    # Store each recipe as a separate document
    for recipe in recipes:
        recipe_id = recipe.get("id")
        if not recipe_id:
            continue

        # Add generatedAt timestamp to each recipe
        recipe_data = {
            **recipe,
            "generatedAt": firestore.SERVER_TIMESTAMP,
        }

        # Add recipe document to batch
        recipe_doc_ref = recipes_collection_ref.document(recipe_id)
        batch.set(recipe_doc_ref, recipe_data)

    # Update user document with metadata
    user_metadata = {
        "lastUpdated": firestore.SERVER_TIMESTAMP,
        "totalRecipes": firestore.Increment(len(recipes))
    }

    # Check if user document exists, if not create it
    if not user_doc_ref.get().exists:
        user_metadata["createdAt"] = firestore.SERVER_TIMESTAMP
        user_metadata["totalRecipes"] = len(recipes)  # Don't use Increment for new documents

    batch.set(user_doc_ref, user_metadata, merge=True)

    # Commit the batch
    batch.commit()

    print(f"Successfully stored {len(recipes)} recipes for user {uid}")


@firestore_fn.on_document_created(document="dietPreferences/{uid}", secrets=["OPENAI_API_KEY"], timeout_sec=500)
def generate_recipes_on_diet_preferences_created(event: firestore_fn.Event[firestore_fn.DocumentSnapshot]) -> None:
    """
    Generate 4 recipes (1 breakfast, 1 lunch, 1 dinner, 1 dessert) when a new dietPreferences document is created.
    This function is triggered automatically when a user completes onboarding and their diet preferences are saved.
    """
    try:
        # Get the UID from the document path
        uid = event.params["uid"]

        # Get the diet preferences from the created document
        diet_preferences = event.data.to_dict()

        if not diet_preferences:
            print(f"No diet preferences found for user {uid}")
            return

        print(f"Generating recipes for user {uid} with preferences: {diet_preferences}")

        # Step 1: Generate recipes using OpenAI
        recipes = generate_recipes_openai(diet_preferences, uid, 4, [])

        # Step 2: Store recipes in Firestore
        store_recipes(uid, recipes)

        print(f"Successfully generated and stored {len(recipes)} recipes for user {uid}")

    except Exception as e:
        print(f"Error generating recipes for user {event.params.get('uid', 'unknown')}: {str(e)}")
        # Don't raise the exception to avoid retries for this background function


@scheduler_fn.on_schedule(schedule="0 0 * * *", timezone="UTC", secrets=["OPENAI_API_KEY"], timeout_sec=600)
def generate_recipes_daily(_event: scheduler_fn.ScheduledEvent) -> None:
    """
    Scheduled function that runs every 24 hours at midnight UTC to generate fresh recipes
    for weekly active users who have diet preferences stored in Firestore.

    This function:
    1. Queries users collection for users active within the last 7 days (based on lastActiveAt field)
    2. For each active user, checks if they have diet preferences
    3. Generates 3 new recipes (1 breakfast, 1 lunch, 1 dinner) for active users with diet preferences
    4. Stores the recipes in Firestore, appending to existing recipes

    Schedule: "0 0 * * *" means:
    - 0 minutes past the hour
    - 0 hours (midnight)
    - Every day of the month
    - Every month
    - Every day of the week
    """
    print("Starting daily recipe generation for weekly active users...")

    try:
        from datetime import datetime, timedelta

        # Calculate the date 7 days ago
        seven_days_ago = datetime.now() - timedelta(days=7)
        print(f"Looking for users active since: {seven_days_ago}")

        # Get weekly active users from users collection
        db = get_db()
        users_collection = db.collection('users')

        # Query users who have been active within the last 7 days
        active_users_query = users_collection.where('lastActiveAt', '>=', seven_days_ago)
        active_users_docs = active_users_query.stream()

        users_processed = 0
        users_failed = 0
        users_skipped = 0
        total_recipes_generated = 0

        for user_doc in active_users_docs:
            uid = user_doc.id
            user_data = user_doc.to_dict()
            last_active = user_data.get('lastActiveAt')

            print(f"Processing active user {uid} (last active: {last_active})")

            try:
                # Check if user has diet preferences
                diet_prefs_ref = db.collection('dietPreferences').document(uid)
                diet_prefs_doc = diet_prefs_ref.get()

                if not diet_prefs_doc.exists:
                    print(f"User {uid} is active but has no diet preferences, skipping")
                    users_skipped += 1
                    continue

                diet_preferences = diet_prefs_doc.to_dict()
                print(f"Generating recipes for active user {uid}")

                # Get existing recipe IDs to avoid duplicates
                existing_recipe_ids = get_existing_recipe_ids(uid)
                print(f"User {uid} has {len(existing_recipe_ids)} existing recipes")

                # Generate recipes using OpenAI, passing existing recipe IDs to avoid duplicates
                recipes = generate_recipes_openai(diet_preferences, uid, 3, existing_recipe_ids)

                # Store recipes in Firestore
                store_recipes(uid, recipes)

                users_processed += 1
                total_recipes_generated += len(recipes)
                print(f"Successfully generated {len(recipes)} recipes for user {uid}")

            except Exception as user_error:
                users_failed += 1
                print(f"Failed to generate recipes for user {uid}: {str(user_error)}")
                # Continue processing other users even if one fails
                continue

        print(f"Daily recipe generation completed. Active users processed: {users_processed}, Failed: {users_failed}, Skipped (no diet prefs): {users_skipped}, Total recipes generated: {total_recipes_generated}")

    except Exception as e:
        print(f"Error in daily recipe generation: {str(e)}")