[debug] [2025-10-03T02:15:10.707Z] ----------------------------------------------------------------------
[debug] [2025-10-03T02:15:10.711Z] Command:       /Users/<USER>/.nvm/versions/node/v20.19.5/bin/node /opt/homebrew/bin/firebase deploy --only functions
[debug] [2025-10-03T02:15:10.711Z] CLI Version:   14.18.0
[debug] [2025-10-03T02:15:10.712Z] Platform:      darwin
[debug] [2025-10-03T02:15:10.712Z] Node Version:  v20.19.5
[debug] [2025-10-03T02:15:10.712Z] Time:          Thu Oct 02 2025 21:15:10 GMT-0500 (Central Daylight Time)
[debug] [2025-10-03T02:15:10.712Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-10-03T02:15:10.806Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-10-03T02:15:10.806Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-10-03T02:15:10.806Z] [iam] checking project chefpal-a9abe for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-10-03T02:15:10.807Z] Checked if tokens are valid: false, expires at: 1759455828444
[debug] [2025-10-03T02:15:10.807Z] Checked if tokens are valid: false, expires at: 1759455828444
[debug] [2025-10-03T02:15:10.807Z] > refreshing access token with scopes: []
[debug] [2025-10-03T02:15:10.810Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-10-03T02:15:10.810Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-10-03T02:15:10.937Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-10-03T02:15:10.937Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-10-03T02:15:10.950Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions [none]
[debug] [2025-10-03T02:15:10.950Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions x-goog-quota-user=projects/chefpal-a9abe
[debug] [2025-10-03T02:15:10.950Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-10-03T02:15:11.127Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions 200
[debug] [2025-10-03T02:15:11.127Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-10-03T02:15:11.128Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.128Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.129Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-10-03T02:15:11.129Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-10-03T02:15:11.318Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-10-03T02:15:11.318Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/chefpal-a9abe/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'chefpal-a9abe'...
[info] 
[info] i  deploying functions 
[debug] [2025-10-03T02:15:11.323Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.323Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.324Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe [none]
[debug] [2025-10-03T02:15:11.404Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe 200
[debug] [2025-10-03T02:15:11.404Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe {"projectNumber":"************","projectId":"chefpal-a9abe","lifecycleState":"ACTIVE","name":"chefpal","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-03-21T02:56:02.438112Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-10-03T02:15:11.405Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.405Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.405Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/chefpal-a9abe/adminSdkConfig [none]
[debug] [2025-10-03T02:15:11.718Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/chefpal-a9abe/adminSdkConfig 200
[debug] [2025-10-03T02:15:11.718Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/chefpal-a9abe/adminSdkConfig {"projectId":"chefpal-a9abe","storageBucket":"chefpal-a9abe.firebasestorage.app"}
[debug] [2025-10-03T02:15:11.719Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.719Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-10-03T02:15:11.719Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/chefpal-a9abe/configs [none]
[debug] [2025-10-03T02:15:11.950Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/chefpal-a9abe/configs 200
[debug] [2025-10-03T02:15:11.951Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/chefpal-a9abe/configs {}
[debug] [2025-10-03T02:15:11.953Z] Customer code is not Node
[debug] [2025-10-03T02:15:11.954Z] Validating python source
[debug] [2025-10-03T02:15:11.954Z] Building python source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[debug] [2025-10-03T02:15:11.955Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-10-03T02:15:11.971Z] Running command with virtualenv: command=., args=["\"/Users/<USER>/Desktop/mobileapp/functions/venv/bin/activate\"","&&","python3.12","-c","\"import firebase_functions; import os; print(os.path.dirname(firebase_functions.__file__))\""]
